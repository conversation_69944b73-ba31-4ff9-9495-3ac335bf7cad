<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CubeJS Assistant</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        #query {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .response {
            white-space: pre-wrap;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
        .status {
            font-style: italic;
            color: #666;
        }
        .tool-call {
            color: #0066cc;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Apella Insights Assistant</h1>
        <div class="input-container">
            <input type="textarea" id="query" placeholder="Ask a question about your data...">
            <button id="submit">Ask</button>
        </div>
        <div class="status" id="status">Ready</div>
        <div class="response" id="response"></div>
    </div>

    <script>
        const queryInput = document.getElementById('query');
        const submitButton = document.getElementById('submit');
        const statusElement = document.getElementById('status');
        const responseElement = document.getElementById('response');

        submitButton.addEventListener('click', async () => {
            const query = queryInput.value.trim();
            if (!query) return;

            // Clear previous response and update status
            responseElement.textContent = '';
            statusElement.textContent = 'Processing...';
            submitButton.disabled = true;

            try {
                const response = await fetch('/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query }),
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const text = decoder.decode(value);
                    const lines = text.split('\n').filter(line => line.trim());

                    for (const line of lines) {
                        try {
                            const event = JSON.parse(line);

                            switch (event.type) {
                                case 'start':
                                    statusElement.textContent = 'Streaming response...';
                                    break;
                                case 'delta':
                                    responseElement.textContent += event.content;
                                    break;
                                case 'agent_update':
                                    statusElement.textContent = `Agent updated: ${event.name}`;
                                    break;
                                case 'tool_call':
                                    statusElement.textContent = `Calling tool: ${event.tool}`;
                                    break;
                                case 'tool_output':
                                    // Optionally show tool outputs
                                    // const toolOutput = document.createElement('div');
                                    // toolOutput.className = 'tool-call';
                                    // toolOutput.textContent = `Tool output: ${event.content}`;
                                    // responseElement.appendChild(toolOutput);
                                    break;
                                case 'message':
                                    // If we get a message output, replace the current response
                                    responseElement.textContent = event.content;
                                    break;
                                case 'end':
                                    statusElement.textContent = 'Completed';
                                    break;
                                case 'error':
                                    statusElement.textContent = `Error: ${event.content}`;
                                    break;
                            }
                        } catch (e) {
                            console.error('Error parsing event:', e, line);
                        }
                    }
                }
            } catch (error) {
                statusElement.textContent = `Error: ${error.message}`;
                console.error('Error:', error);
            } finally {
                submitButton.disabled = false;
            }
        });

        // Allow pressing Enter to submit
        queryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                submitButton.click();
            }
        });
    </script>
</body>
</html>
