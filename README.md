# CubeJS Assistant API Server

This project provides a FastAPI server that exposes the CubeJS Assistant as an API with streaming support.

## Features

- **REST API**: Query the CubeJS Assistant through a simple REST API
- **Streaming Responses**: Get real-time streaming responses as they're generated
- **Web Interface**: Simple web interface for interacting with the assistant

## Requirements

- Python 3.9+
- FastAPI
- Uvicorn
- OpenAI API key and other required environment variables

## Installation

1. Make sure you have all the required environment variables set:
   - `OPENAI_API_KEY`
   - `CUBE_API_URL`
   - `CUBEJS_CLIENT_ID`
   - `CUBEJS_CLIENT_SECRET`
   - `AUTH_URL`
   - `AUDIENCE`

2. Install the required dependencies:
   ```bash
   pip install fastapi uvicorn openai-agents
   ```

## Running the Server

Run the server with:

```bash
python server.py
```

The server will start at http://0.0.0.0:8000

## API Endpoints

### 1. Query Endpoint (Non-streaming)

```
POST /query
```

Request body:
```json
{
  "query": "Your question about the data"
}
```

Response:
```json
{
  "response": "The assistant's response"
}
```

### 2. Streaming Endpoint

```
POST /stream
```

Request body:
```json
{
  "query": "Your question about the data"
}
```

Response: A stream of newline-delimited JSON objects with the following format:

```json
{"type": "start"}
{"type": "delta", "content": "The"}
{"type": "delta", "content": " assistant's"}
{"type": "delta", "content": " response"}
{"type": "tool_call", "tool": "tool_name"}
{"type": "tool_output", "content": "Tool output"}
{"type": "end"}
```

## Web Interface

A simple web interface is available at the root URL (`/`). It provides a user-friendly way to interact with the assistant and see streaming responses in real-time.
