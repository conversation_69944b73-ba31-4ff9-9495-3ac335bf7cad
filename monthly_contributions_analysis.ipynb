#%% md
# Monthly Contributions Analysis

This notebook analyzes team member contributions across GitHub and Linear over the past 3 months, generating visualizations for:
1. Number of GitHub PRs merged
2. Sum of Linear effort for tickets in 'Done' status
3. Number of GitHub PRs reviewed
#%% md
## Setup and Imports
#%%
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import requests
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 6)
plt.rcParams['font.size'] = 10
#%% md
## Configuration
#%%
# API Configuration
LINEAR_API_KEY = os.getenv("LINEAR_API_KEY")
GITHUB_API_KEY = os.getenv("GITHUB_API_KEY")

# Team Members
TEAM_MEMBERS = {
    "andrea": {"github": "andreab-apella", "email": "<EMAIL>", "name": "<PERSON>"},
    "sam": {"github": "samraper", "email": "<EMAIL>", "name": "Sam"},
    "selam": {"github": "smoges25", "email": "<EMAIL>", "name": "Selam"},
    "jesse": {"github": "jrmils89", "email": "<EMAIL>", "name": "Jesse"},
    "luke": {"github": "infiniteluke", "email": "<EMAIL>", "name": "Luke"},
    "darren": {"github": "darreneng", "email": "<EMAIL>", "name": "Darren"},
    "ami": {"github": "amipatel2", "email": "<EMAIL>", "name": "Ami"},
    "anna": {"github": "anna-poz", "email": "<EMAIL>", "name": "Anna"},
    "na": {"github": "Na-Apella", "email": "<EMAIL>", "name": "Na"},
}

# API Endpoints and Headers
LINEAR_ENDPOINT = "https://api.linear.app/graphql"
LINEAR_HEADERS = {"Authorization": LINEAR_API_KEY}
GITHUB_HEADERS = {"Authorization": f"Bearer {GITHUB_API_KEY}"}
GITHUB_SEARCH_ENDPOINT = "https://api.github.com/search/issues"

# Check API keys
if not LINEAR_API_KEY:
    print("⚠️  Warning: LINEAR_API_KEY environment variable not set")
if not GITHUB_API_KEY:
    print("⚠️  Warning: GITHUB_API_KEY environment variable not set")
    
print("✅ Configuration loaded successfully")
#%% md
## Utility Functions
#%%
def get_past_three_months():
    """Get the past 3 months as a list of (start_date, end_date, month_str) tuples."""
    today = datetime.now()
    months = []
    
    for i in range(3):
        # Go back i months from current month
        month_date = today - relativedelta(months=i)
        
        # Get first day of the month
        start_date = month_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # Get last day of the month
        if month_date.month == 12:
            end_date = month_date.replace(year=month_date.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_date = month_date.replace(month=month_date.month + 1, day=1) - timedelta(days=1)
        end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        month_str = month_date.strftime("%Y-%m")
        months.append((start_date, end_date, month_str))
    
    return months

# Test the function
months = get_past_three_months()
print("Date ranges for analysis:")
for start, end, month_str in months:
    print(f"  {month_str}: {start.strftime('%Y-%m-%d')} to {end.strftime('%Y-%m-%d')}")
#%% md
## Linear API Functions
#%%
# Updated Linear query to include estimate field
LINEAR_QUERY = """
query ($assigneeEmail: String!, $start: DateTimeOrDuration!, $end: DateTimeOrDuration!) {
  issues(filter: {
    assignee: { email: { eq: $assigneeEmail } },
    completedAt: { gte: $start, lte: $end },
    state: { name: { eq: "Done" } }
  }) {
    nodes {
      title
      url
      completedAt
      estimate
    }
  }
}
"""

def get_linear_done_tickets_with_effort(user_email, start_date, end_date):
    """Get count and sum of effort for Linear tickets marked as Done for a user in a date range."""
    start_iso = start_date.isoformat() + "Z"
    end_iso = end_date.isoformat() + "Z"
    
    response = requests.post(
        LINEAR_ENDPOINT,
        headers=LINEAR_HEADERS,
        json={
            "query": LINEAR_QUERY, 
            "variables": {
                "assigneeEmail": user_email, 
                "start": start_iso, 
                "end": end_iso
            }
        }
    )
    
    if response.status_code != 200:
        print(f"Linear API error for {user_email}: {response.status_code} - {response.text}")
        return 0, 0
    
    resp = response.json()
    if "errors" in resp:
        print(f"Linear GraphQL errors for {user_email}: {resp['errors']}")
        return 0, 0
    
    issues = resp["data"]["issues"]["nodes"]
    count = len(issues)
    
    # Sum up the effort estimates (handle None values)
    total_effort = sum(issue.get("estimate", 0) or 0 for issue in issues)
    
    return count, total_effort

print("✅ Linear API functions defined")
#%% md
## GitHub API Functions
#%%
def get_github_merged_prs_count(username, start_date, end_date):
    """Get count of merged PRs authored by a user in a date range."""
    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")
    
    query = f"author:{username} org:Apella-Technology is:pr is:merged merged:{start_str}..{end_str}"
    
    response = requests.get(
        GITHUB_SEARCH_ENDPOINT,
        headers=GITHUB_HEADERS,
        params={"q": query, "per_page": 100}
    )
    
    if response.status_code != 200:
        print(f"GitHub API error for {username} merged PRs: {response.status_code} - {response.text}")
        return 0
    
    resp = response.json()
    return resp.get("total_count", 0)

def get_github_reviewed_prs_count(username, start_date, end_date):
    """Get count of PRs reviewed by a user in a date range."""
    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")
    
    query = f"reviewed-by:{username} org:Apella-Technology is:pr updated:{start_str}..{end_str}"
    
    response = requests.get(
        GITHUB_SEARCH_ENDPOINT,
        headers=GITHUB_HEADERS,
        params={"q": query, "per_page": 100}
    )
    
    if response.status_code != 200:
        print(f"GitHub API error for {username} reviewed PRs: {response.status_code} - {response.text}")
        return 0
    
    resp = response.json()
    return resp.get("total_count", 0)

print("✅ GitHub API functions defined")
#%% md
## Data Collection
#%%
def collect_contributions_data():
    """Collect contribution data for all team members over the past 3 months."""
    months = get_past_three_months()
    data = []
    
    print("Fetching contribution data...")
    
    for person_key, person_info in TEAM_MEMBERS.items():
        print(f"\nProcessing {person_info['name']}...")
        
        for start_date, end_date, month_str in months:
            print(f"  Month: {month_str}")
            
            # Get Linear done tickets and effort
            linear_done_tickets, linear_effort = get_linear_done_tickets_with_effort(
                person_info["email"], start_date, end_date
            )
            
            # Get GitHub merged PRs
            github_prs_merged = get_github_merged_prs_count(
                person_info["github"], start_date, end_date
            )
            
            # Get GitHub reviewed PRs
            github_prs_reviewed = get_github_reviewed_prs_count(
                person_info["github"], start_date, end_date
            )
            
            data.append({
                "person": person_info["name"],
                "month": month_str,
                "github_prs_merged": github_prs_merged,
                "linear_done_tickets": linear_done_tickets,
                "linear_effort": linear_effort,
                "github_prs_reviewed": github_prs_reviewed
            })
            
            print(f"    Linear tickets: {linear_done_tickets} (effort: {linear_effort}), "
                  f"Merged PRs: {github_prs_merged}, "
                  f"Reviewed PRs: {github_prs_reviewed}")
    
    return pd.DataFrame(data)

# Collect the data
df = collect_contributions_data()
print(f"\n✅ Data collection complete! Collected {len(df)} records.")
print("\nFirst few rows:")
df.head()
#%% md
## Data Overview
#%%
# Display data summary
print("Data Summary:")
print(f"Total records: {len(df)}")
print(f"Team members: {df['person'].nunique()}")
print(f"Months covered: {sorted(df['month'].unique())}")
print("\nSample data:")
display(df.head(10))

print("\nData types:")
print(df.dtypes)

print("\nBasic statistics:")
display(df.describe())
#%% md
## Data Visualization

### Graph 1: GitHub PRs Merged
#%%
# Prepare data for plotting
pivot_merged = df.pivot(index='month', columns='person', values='github_prs_merged').fillna(0)

# Sort months chronologically
pivot_merged = pivot_merged.sort_index()

# Create the plot
plt.figure(figsize=(14, 8))
for person in pivot_merged.columns:
    plt.plot(pivot_merged.index, pivot_merged[person], marker='o', linewidth=2, label=person)

plt.title('GitHub PRs Merged by Team Member Over Time', fontsize=16, fontweight='bold')
plt.xlabel('Month', fontsize=12)
plt.ylabel('Number of PRs Merged', fontsize=12)
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Show summary statistics
print("\nSummary - GitHub PRs Merged:")
print(pivot_merged.sum().sort_values(ascending=False))
#%% md
### Graph 2: Linear Effort for Done Tickets
#%%
# Prepare data for plotting
pivot_effort = df.pivot(index='month', columns='person', values='linear_effort').fillna(0)

# Sort months chronologically
pivot_effort = pivot_effort.sort_index()

# Create the plot
plt.figure(figsize=(14, 8))
for person in pivot_effort.columns:
    plt.plot(pivot_effort.index, pivot_effort[person], marker='s', linewidth=2, label=person)

plt.title('Linear Effort Points for Done Tickets by Team Member Over Time', fontsize=16, fontweight='bold')
plt.xlabel('Month', fontsize=12)
plt.ylabel('Total Effort Points', fontsize=12)
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Show summary statistics
print("\nSummary - Linear Effort Points:")
print(pivot_effort.sum().sort_values(ascending=False))
#%% md
### Graph 3: GitHub PRs Reviewed
#%%
# Prepare data for plotting
pivot_reviewed = df.pivot(index='month', columns='person', values='github_prs_reviewed').fillna(0)

# Sort months chronologically
pivot_reviewed = pivot_reviewed.sort_index()

# Create the plot
plt.figure(figsize=(14, 8))
for person in pivot_reviewed.columns:
    plt.plot(pivot_reviewed.index, pivot_reviewed[person], marker='^', linewidth=2, label=person)

plt.title('GitHub PRs Reviewed by Team Member Over Time', fontsize=16, fontweight='bold')
plt.xlabel('Month', fontsize=12)
plt.ylabel('Number of PRs Reviewed', fontsize=12)
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Show summary statistics
print("\nSummary - GitHub PRs Reviewed:")
print(pivot_reviewed.sum().sort_values(ascending=False))
#%% md
## Combined Overview Dashboard
#%%
# Create a combined dashboard with all three metrics
fig, axes = plt.subplots(3, 1, figsize=(16, 18))

# Graph 1: PRs Merged
for person in pivot_merged.columns:
    axes[0].plot(pivot_merged.index, pivot_merged[person], marker='o', linewidth=2, label=person)
axes[0].set_title('GitHub PRs Merged by Team Member Over Time', fontsize=14, fontweight='bold')
axes[0].set_ylabel('Number of PRs Merged', fontsize=11)
axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
axes[0].grid(True, alpha=0.3)
axes[0].tick_params(axis='x', rotation=45)

# Graph 2: Linear Effort
for person in pivot_effort.columns:
    axes[1].plot(pivot_effort.index, pivot_effort[person], marker='s', linewidth=2, label=person)
axes[1].set_title('Linear Effort Points for Done Tickets by Team Member Over Time', fontsize=14, fontweight='bold')
axes[1].set_ylabel('Total Effort Points', fontsize=11)
axes[1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
axes[1].grid(True, alpha=0.3)
axes[1].tick_params(axis='x', rotation=45)

# Graph 3: PRs Reviewed
for person in pivot_reviewed.columns:
    axes[2].plot(pivot_reviewed.index, pivot_reviewed[person], marker='^', linewidth=2, label=person)
axes[2].set_title('GitHub PRs Reviewed by Team Member Over Time', fontsize=14, fontweight='bold')
axes[2].set_xlabel('Month', fontsize=11)
axes[2].set_ylabel('Number of PRs Reviewed', fontsize=11)
axes[2].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
axes[2].grid(True, alpha=0.3)
axes[2].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()
#%% md
## Summary Statistics
#%%
# Create summary table
summary_stats = pd.DataFrame({
    'Total PRs Merged': pivot_merged.sum(),
    'Total Effort Points': pivot_effort.sum(),
    'Total PRs Reviewed': pivot_reviewed.sum()
})

# Sort by total PRs merged
summary_stats = summary_stats.sort_values('Total PRs Merged', ascending=False)

print("\n📊 SUMMARY STATISTICS (Past 3 Months)")
print("=" * 50)
display(summary_stats)

# Monthly averages
monthly_averages = pd.DataFrame({
    'Avg PRs Merged/Month': pivot_merged.mean(),
    'Avg Effort Points/Month': pivot_effort.mean(),
    'Avg PRs Reviewed/Month': pivot_reviewed.mean()
})

monthly_averages = monthly_averages.sort_values('Avg PRs Merged/Month', ascending=False)

print("\n📈 MONTHLY AVERAGES")
print("=" * 30)
display(monthly_averages.round(1))
#%% md
## Export Data
#%%
# Save the data to CSV for future reference
output_filename = f"monthly_contributions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
df.to_csv(output_filename, index=False)
print(f"✅ Data exported to: {output_filename}")

# Also save summary statistics
summary_filename = f"monthly_contributions_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
summary_stats.to_csv(summary_filename)
print(f"✅ Summary statistics exported to: {summary_filename}")

print(f"\n📋 Analysis complete! Processed {len(df)} records across {df['person'].nunique()} team members.")