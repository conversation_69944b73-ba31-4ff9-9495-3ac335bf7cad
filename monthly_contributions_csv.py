import csv
import os
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import requests

# ========== CONFIGURATION ==========
LINEAR_API_KEY = os.getenv("LINEAR_API_KEY")
GITHUB_API_KEY = os.getenv("GITHUB_API_KEY")

TEAM_MEMBERS = {
    # "andrea": {"github": "andreab-apella", "email": "<EMAIL>", "name": "<PERSON>"},
    # "sam": {"github": "samraper", "email": "<EMAIL>", "name": "Sam"},
    # "selam": {"github": "smoges25", "email": "<EMAIL>", "name": "Selam"},
    # "jesse": {"github": "jrmils89", "email": "<EMAIL>", "name": "<PERSON>"},
    # "luke": {"github": "infiniteluke", "email": "<EMAIL>", "name": "<PERSON>"},
    # "darren": {"github": "darreneng", "email": "<EMAIL>", "name": "Darren"},
    # "ami": {"github": "amipatel2", "email": "<EMAIL>", "name": "Ami"},
    # "anna": {"github": "anna-poz", "email": "<EMAIL>", "name": "Anna"},
    # "na": {"github": "Na-Apella", "email": "<EMAIL>", "name": "Na"},
    "chase": {"github": "chasebro", "email": "<EMAIL>", "name": "Chase"},
}

# ========== DATE UTILITIES ==========
def get_past_three_months():
    """Get the past 3 months as a list of (start_date, end_date, month_str) tuples."""
    today = datetime.now()
    months = []
    
    for i in range(3):
        # Go back i months from current month
        month_date = today - relativedelta(months=i)
        
        # Get first day of the month
        start_date = month_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # Get last day of the month
        if month_date.month == 12:
            end_date = month_date.replace(year=month_date.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_date = month_date.replace(month=month_date.month + 1, day=1) - timedelta(days=1)
        end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        month_str = month_date.strftime("%Y-%m")
        months.append((start_date, end_date, month_str))
    
    return months

# ========== LINEAR API ==========
LINEAR_ENDPOINT = "https://api.linear.app/graphql"
LINEAR_HEADERS = {"Authorization": LINEAR_API_KEY}

LINEAR_QUERY = """
query ($assigneeEmail: String!, $start: DateTimeOrDuration!, $end: DateTimeOrDuration!) {
  issues(filter: {
    assignee: { email: { eq: $assigneeEmail } },
    completedAt: { gte: $start, lte: $end },
    state: { name: { eq: "Done" } }
  }) {
    nodes {
      title
      url
      completedAt
    }
  }
}
"""

def get_linear_done_tickets(user_email, start_date, end_date):
    """Get count of Linear tickets marked as Done for a user in a date range."""
    start_iso = start_date.isoformat() + "Z"
    end_iso = end_date.isoformat() + "Z"
    
    response = requests.post(
        LINEAR_ENDPOINT,
        headers=LINEAR_HEADERS,
        json={
            "query": LINEAR_QUERY, 
            "variables": {
                "assigneeEmail": user_email, 
                "start": start_iso, 
                "end": end_iso
            }
        }
    )
    
    if response.status_code != 200:
        print(f"Linear API error for {user_email}: {response.status_code} - {response.text}")
        return 0
    
    resp = response.json()
    if "errors" in resp:
        print(f"Linear GraphQL errors for {user_email}: {resp['errors']}")
        return 0
    
    return len(resp["data"]["issues"]["nodes"])

# ========== GITHUB API ==========
GITHUB_HEADERS = {"Authorization": f"Bearer {GITHUB_API_KEY}"}
GITHUB_SEARCH_ENDPOINT = "https://api.github.com/search/issues"

def get_github_merged_prs_count(username, start_date, end_date):
    """Get count of merged PRs authored by a user in a date range."""
    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")
    
    query = f"author:{username} org:Apella-Technology is:pr is:merged merged:{start_str}..{end_str}"
    
    response = requests.get(
        GITHUB_SEARCH_ENDPOINT,
        headers=GITHUB_HEADERS,
        params={"q": query, "per_page": 100}
    )
    
    if response.status_code != 200:
        print(f"GitHub API error for {username} merged PRs: {response.status_code} - {response.text}")
        return 0
    
    resp = response.json()
    return resp.get("total_count", 0)

def get_github_reviewed_prs_count(username, start_date, end_date):
    """Get count of PRs reviewed by a user in a date range."""
    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")
    
    query = f"reviewed-by:{username} org:Apella-Technology is:pr updated:{start_str}..{end_str}"
    
    response = requests.get(
        GITHUB_SEARCH_ENDPOINT,
        headers=GITHUB_HEADERS,
        params={"q": query, "per_page": 100}
    )
    
    if response.status_code != 200:
        print(f"GitHub API error for {username} reviewed PRs: {response.status_code} - {response.text}")
        return 0
    
    resp = response.json()
    return resp.get("total_count", 0)

# ========== CSV GENERATION ==========
def generate_contributions_csv():
    """Generate CSV with monthly contributions for all team members over the past 3 months."""
    months = get_past_three_months()
    
    # Prepare CSV data
    csv_data = []
    
    print("Fetching contribution data...")
    
    for person_key, person_info in TEAM_MEMBERS.items():
        print(f"Processing {person_info['name']}...")
        
        for start_date, end_date, month_str in months:
            print(f"  Month: {month_str}")
            
            # Get Linear done tickets
            linear_done_tickets = get_linear_done_tickets(
                person_info["email"], start_date, end_date
            )
            
            # Get GitHub merged PRs
            github_prs_merged = get_github_merged_prs_count(
                person_info["github"], start_date, end_date
            )
            
            # Get GitHub reviewed PRs
            github_prs_reviewed = get_github_reviewed_prs_count(
                person_info["github"], start_date, end_date
            )
            
            csv_data.append({
                "person": person_info["name"],
                "month": month_str,
                "github_prs_merged": github_prs_merged,
                "linear_done_tickets": linear_done_tickets,
                "github_prs_reviewed": github_prs_reviewed
            })
            
            print(f"    Linear tickets: {linear_done_tickets}, "
                  f"Merged PRs: {github_prs_merged}, "
                  f"Reviewed PRs: {github_prs_reviewed}")
    
    # Write to CSV
    output_filename = f"monthly_contributions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    with open(output_filename, 'w', newline='') as csvfile:
        fieldnames = ["person", "month", "github_prs_merged", "linear_done_tickets", "github_prs_reviewed"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in csv_data:
            writer.writerow(row)
    
    print(f"\nCSV file generated: {output_filename}")
    print(f"Total rows: {len(csv_data)}")
    
    return output_filename

# ========== MAIN ==========
def main():
    """Main function to generate the monthly contributions CSV."""
    if not LINEAR_API_KEY:
        print("Error: LINEAR_API_KEY environment variable not set")
        return
    
    if not GITHUB_API_KEY:
        print("Error: GITHUB_API_KEY environment variable not set")
        return
    
    generate_contributions_csv()

if __name__ == "__main__":
    main()
