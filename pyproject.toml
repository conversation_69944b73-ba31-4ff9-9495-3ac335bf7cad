[tool.poetry]
name = "scripts"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.9"
notion-client = "^2.3.0"
slack-sdk = "^3.34.0"
google = "^3.0.0"
requests = "^2.32.3"
openai = "^1.70.0"
pandas = "^2.2.3"
holidays = "^0.70"
google-api-python-client = "^2.167.0"
google-auth-httplib2 = "^0.2.0"
google-auth-oauthlib = "^1.2.1"
openai-agents = "^0.0.12"
fastapi = "^0.115.12"
uvicorn = "^0.34.2"
python-multipart = "^0.0.20"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
