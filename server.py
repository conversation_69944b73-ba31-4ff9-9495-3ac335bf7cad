"""
CubeJS Assistant API Server

This module provides a FastAPI server that exposes the CubeJS Assistant as an API.
It supports streaming responses for real-time interaction.
"""

import asyncio
import j<PERSON>
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

# Import the agent and streaming functionality from the main module
from openai_insights_llm_hack import agent, <PERSON>, ItemHelpers

app = FastAPI(
    title="CubeJS Assistant API",
    description="API for interacting with the CubeJS Assistant",
    version="1.0.0",
)

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Add CORS middleware to allow cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

class QueryRequest(BaseModel):
    """Request model for the query endpoint."""
    query: str

@app.post("/query")
async def query(request: QueryRequest):
    """
    Process a query and return the response.

    This is a non-streaming endpoint that returns the complete response.
    """
    try:
        result = await Runner.run(agent, request.query)
        return {"response": result.final_output}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def stream_generator(query: str):
    """Generate streaming events for the query."""
    try:
        result = Runner.run_streamed(agent, query)

        # Send a start marker
        yield json.dumps({"type": "start"}) + "\n"

        async for event in result.stream_events():
            # We'll ignore some event types to keep the stream clean
            if event.type == "raw_response_event" and hasattr(event.data, "delta"):
                # For text deltas, send the content
                yield json.dumps({
                    "type": "delta",
                    "content": event.data.delta
                }) + "\n"
            elif event.type == "agent_updated_stream_event":
                # Agent was updated
                yield json.dumps({
                    "type": "agent_update",
                    "name": event.new_agent.name
                }) + "\n"
            elif event.type == "run_item_stream_event":
                if event.item.type == "tool_call_item":
                    # Tool was called
                    item = event.item.raw_item
                    tool = item.name if hasattr(item, "name") else item.type
                    yield json.dumps({
                        "type": "tool_call",
                        "tool": tool
                    }) + "\n"
                elif event.item.type == "tool_call_output_item":
                    # Tool output
                    yield json.dumps({
                        "type": "tool_output",
                        "content": str(event.item.output)
                    }) + "\n"
                elif event.item.type == "message_output_item":
                    # Message output
                    yield json.dumps({
                        "type": "message",
                        "content": ItemHelpers.text_message_output(event.item)
                    }) + "\n"

        # Send an end marker
        yield json.dumps({"type": "end"}) + "\n"
    except Exception as e:
        # Send an error message
        yield json.dumps({
            "type": "error",
            "content": str(e)
        }) + "\n"

@app.post("/stream")
async def stream_query(request: Request):
    """
    Process a query and stream the response.

    This endpoint streams the response as it's generated, allowing for real-time updates.
    """
    try:
        # Parse the request body
        body = await request.json()
        query = body.get("query")

        if not query:
            raise HTTPException(status_code=400, detail="Query parameter is required")

        # Return a streaming response
        return StreamingResponse(
            stream_generator(query),
            media_type="application/x-ndjson"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/", response_class=HTMLResponse)
async def get_index():
    """Serve the index.html file."""
    with open("static/index.html", "r") as f:
        return f.read()

if __name__ == "__main__":
    import uvicorn
    print("Starting server at http://0.0.0.0:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
