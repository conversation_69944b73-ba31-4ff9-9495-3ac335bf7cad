"""
cubejs_openai_agents.py

Refactor of the previous Lang<PERSON>hai<PERSON>/<PERSON>h implementation to the OpenAI Agents SDK (May 2025).
Keeps CubeJS querying, timezone discovery, schema/OR retrieval, and chat Q&A, but now
leverages the built‑in `Agent` loop and `@function_tool` decorator.
"""
import asyncio
import os
import time
import json
import pathlib
from functools import lru_cache
from typing import List, Dict, Optional, Tuple, Literal, Union

import requests
from openai import OpenAI
from openai.lib.streaming.responses import ResponseTextDeltaEvent
from openai.types import Reasoning
from pydantic import BaseModel, Field, model_validator

# OpenAI Agents SDK
from agents import Agent, Runner, ModelSettings, function_tool, FileSearchTool, RunContextWrapper, \
    ItemHelpers, set_tracing_disabled

# ---------------------------------------------------------------------------
# 1. Environment + constants
# ---------------------------------------------------------------------------

cubejs_domain = os.environ.get("CUBE_API_URL", "https://cubejs.apella.io/cubejs-api")
openai_api_key = os.environ["OPENAI_API_KEY"]
cubejs_client_id = os.environ["CUBEJS_CLIENT_ID"]
cubejs_client_secret = os.environ["CUBEJS_CLIENT_SECRET"]
set_tracing_disabled(True)


@lru_cache
def settings() -> Dict[str, str]:
    """Centralised env‑vars – expands once then cached."""
    return {
        "CUBE_API_URL": cubejs_domain,
        "OPENAI_API_KEY": openai_api_key,
        "EMBED_MODEL": os.getenv("EMBED_MODEL", "text-embedding-3-small"),
    }


_cfg = settings()


# ---------------------------------------------------------------------------
# 2. CubeJS helpers
# ---------------------------------------------------------------------------

def get_new_auth_token() -> str:
    """Obtain & cache a short‑lived JWT for CubeJS via Auth0."""
    auth_url = os.environ["AUTH_URL"]
    audience = os.environ["AUDIENCE"]
    organization = os.environ.get("CUBE_ORG", "org_Xj37YbZTAOh54edr")  # Houston Methodist

    token_env_key = "CUBE_API_TOKEN"
    cached = os.getenv(token_env_key)
    if cached:
        res = requests.get("%s/v1/meta" % cubejs_domain, headers={"Authorization": cached},
                           timeout=5)
        if res.status_code == 200:
            return cached

    payload = {
        "client_id": cubejs_client_id,
        "client_secret": cubejs_client_secret,
        "audience": audience,
        "grant_type": "client_credentials",
        "organization": organization,
    }
    r = requests.post(f"{auth_url}/oauth/token", json=payload, timeout=15)
    r.raise_for_status()
    fresh = r.json()["access_token"]
    os.environ[token_env_key] = fresh
    return fresh


def _post_cube(path: str, payload: dict, timeout: int = 30) -> dict:
    url = f"{_cfg['CUBE_API_URL']}/v1/{path.lstrip('/')}"
    headers = {
        "Authorization": get_new_auth_token(),
        "Content-Type": "application/json",
    }
    res = requests.post(url, headers=headers, json=payload, timeout=timeout)
    res.raise_for_status()
    return res.json()


# ---------------------------------------------------------------------------
# 3. Function tools (using @function_tool)
# ---------------------------------------------------------------------------
# ---------- 2.2  Pydantic schema for the tool args ---------- #
# ──────────────────────────────────────────────────────────────────────────────
# 1.  “Leaf” filter  ── member + operator + (optional) values
# ──────────────────────────────────────────────────────────────────────────────


_LEAF_OPS: tuple[str, ...] = (
    # string / number / time
    "equals",
    "notEquals",
    "contains",
    "notContains",
    "startsWith",
    "notStartsWith",
    "endsWith",
    "notEndsWith",
    "gt",
    "gte",
    "lt",
    "lte",
    "set",
    "notSet",
    # time
    "inDateRange",
    "notInDateRange",
    "beforeDate",
    "afterDate",
    "onDate",
    "measureFilter",  # special
)

LeafOperator = Literal[_LEAF_OPS]  # type‑safe auto‑completion


class LeafFilter(BaseModel):
    member: str = Field(
        ...,
        description="Cube dimension or measure to filter, e.g. 'orders.status'",
    )
    operator: LeafOperator
    values: Optional[List[str]] = None

    # --- enforcement: operators that MUST / MUST NOT carry `values` ----------
    _validate_values = model_validator(mode="after")

    def _validate_values(cls, self):
        if self.operator in {"set", "notSet", "measureFilter"}:
            if self.values is not None:
                raise ValueError(f"Operator '{self.operator}' should not have values")
        else:
            if not self.values:
                raise ValueError(f"Operator '{self.operator}' requires a non‑empty 'values' list")
        return self


# ──────────────────────────────────────────────────────────────────────────────
# 2.  Logical filters  ── AND / OR of other filters
# ──────────────────────────────────────────────────────────────────────────────

class LogicalFilter(BaseModel):
    # Python identifiers can't be "and"/"or", so we add aliases
    or_: Optional[List["CubeFilter"]] = Field(None, alias="or")
    and_: Optional[List["CubeFilter"]] = Field(None, alias="and")

    _validate_logic = model_validator(mode="after")

    def _validate_logic(cls, self):
        # Exactly one of `or` or `and` must be present
        if bool(self.or_) == bool(self.and_):
            raise ValueError("Logical filter must contain either 'or' or 'and', not both / neither")
        return self


# ──────────────────────────────────────────────────────────────────────────────
# 3.  Unified recursive type  ── “filter” can be either leaf OR logical
# ──────────────────────────────────────────────────────────────────────────────

CubeFilter = Union[LeafFilter, LogicalFilter]

# Let Pydantic (re)‑process forward refs in LogicalFilter
LogicalFilter.model_rebuild()

DateStr = str  # YYYY‑MM‑DD or ISO timestamp
DateRange = List[DateStr]  # 1‑item or 2‑item array
RelativeRange = str  # e.g. "last quarter", "today"


class TimeDimension(BaseModel):
    """
    Cube timeDimension format:
      • dimension (required)
      • dateRange ← array of ISO dates OR a relative‑range string
      • compareDateRange ← array of date ranges to compare
      • granularity ← month / day / year / custom, etc.
    """

    dimension: str = Field(..., description="Time dimension name, e.g. 'orders.createdAt'")
    dateRange: Optional[Union[DateRange, RelativeRange]] = Field(
        None,
        description="Either ['YYYY‑MM‑DD', 'YYYY‑MM‑DD'] or a relative string like 'last week'",
    )
    compareDateRange: Optional[List[DateRange]] = Field(
        None,
        description="Array of date ranges used for compare‑to‑past queries",
    )
    granularity: Optional[str] = Field(
        None,
        description="Time granularity (day, week, month, … or custom)",
    )

    # --- basic sanity checks -------------------------------------------------
    @model_validator(mode="after")
    def _validate_ranges(self):
        if self.dateRange and self.compareDateRange:
            raise ValueError("Use either 'dateRange' OR 'compareDateRange', not both")

        if isinstance(self.dateRange, list) and not (1 <= len(self.dateRange) <= 2):
            raise ValueError("'dateRange' list must have 1 or 2 items")

        if self.compareDateRange:
            for sub in self.compareDateRange:
                if not (1 <= len(sub) <= 2):
                    raise ValueError("'compareDateRange' sub‑ranges must have 1 or 2 dates")

        return self


class CubeQueryArgs(BaseModel):
    measures: Optional[list[str]] = Field(default=None,
                                          description="Cube measures. Each measure is written as 'cube_name'.'measure_name'")
    dimensions: Optional[list[str]] = None
    filters: Optional[list[CubeFilter]] = Field(
        default=None,
        description=(
            "List of filter objects. Example:\n"
            "[{\"member\": \"Orders.dayOfWeek\", \"operator\": \"equals\", "
            "\"values\": [\"Monday\", \"Wednesday\"]}]"
        ),
    )
    timeDimensions: Optional[list[TimeDimension]] = None
    timezone: str = Field(
        description="You can set the desired time zone in the TZ Database Name format, e.g., America/Los_Angeles.")

    @model_validator(mode="after")
    def _validate_measures_dimensions(self):
        if not self.measures and not self.dimensions:
            raise ValueError("Must provide either measures or dimensions")
        return self

def on_cube_query_failure(run_context: RunContextWrapper, error: Exception) -> str:
    """Handle CubeJS query failures."""
    if type(error) == requests.exceptions.HTTPError and error.response is not None:
        return error.response.json()['error']
    return f"CubeJS query failed: {error}"


@function_tool(failure_error_function=on_cube_query_failure)
def cube_query(query: CubeQueryArgs) -> dict:
    """Run an aggregate query on CubeJS and return the rows plus timing."""
    t0 = time.perf_counter()
    payload = {"query": query.model_dump(exclude_none=True)}
    res = _post_cube("load", payload)
    return {
        "data": res["data"],
        "query": query,
        "elapsed": time.perf_counter() - t0,
    }


@function_tool
def timezone_query() -> str:
    """Return the default timezone for CubeJS queries."""
    payload = {
        "query": {
            "dimensions": ["InsightsCases.casePhaseId", "SitesBQ.timezone"],
            "limit": 1,
        }
    }
    res = _post_cube("load", payload)
    return res["data"][0]["SitesBQ.timezone"]


# ---------------------------------------------------------------------------
# 4. Schema & OR‑concept retrievers (Direct API)
# ---------------------------------------------------------------------------


def fetch_cube_schema() -> List[dict]:
    """Fetch the CubeJS schema directly from the API."""
    headers = {"Authorization": get_new_auth_token()}
    res = requests.get(f"{_cfg['CUBE_API_URL']}/v1/meta", headers=headers, timeout=15)
    res.raise_for_status()
    return res.json().get("cubes", [])


# def _format_schema_result(cube: dict) -> str:
#     """Format a cube schema as a readable string."""
#     snippet = json.dumps(cube, indent=2)
#     return f"Cube **{cube['name']}**\n\n{snippet}"

@function_tool
def schema_search() -> List[str]:
    """
    Fetch the CubeJS schema to understand the allowable data to query from cubejs.
    Always query the schema before querying the cubejs data.
    """
    cubes = fetch_cube_schema()
    # Filter out CustomPhases
    cubes = [cube for cube in cubes if cube["name"] != "CustomPhases"]

    # # Simple text search - convert everything to lowercase for case-insensitive matching
    # query_lower = query.lower()
    # matched_cubes = []
    #
    # for cube in cubes:
    #     # Convert cube to string and check if query appears in it
    #     cube_str = json.dumps(cube).lower()
    #     if query_lower in cube_str or query_lower in cube["name"].lower():
    #         matched_cubes.append(cube)
    #
    # # Sort by relevance (name matches first)
    # matched_cubes.sort(key=lambda c: query_lower in c["name"].lower(), reverse=True)
    #
    # # Return top k results
    # return [_format_schema_result(cube) for cube in matched_cubes[:k]]
    return cubes


# ---------------------------------------------------------------------------
# 6. Agent and runner
# ---------------------------------------------------------------------------

agent = Agent(
    name="CubeJS‑Assistant",
    instructions=(
        "You are a data‑insights assistant for a CubeJS warehouse.\n"
        "Your job is to output data results to the user. Your job is not to coach the user how to query the data.\n"
        "If the user does not specify a date range, default to the last 3 months. You may use relative ranges like 'last month' or 'last quarter' for any time dimension.\n"
        "Always call `timezone_query` first to get the site's timezone before calling `cube_query`.\n"
        "Always call `schema_query` before calling `cube_query` to understand the allowable data to provide. Otherwise expect 500 network errors.\n"
        "When an ID filter yields no rows, run a helper query to enumerate valid IDs.\n"
        "The field InsightsCases.actualStartTimeLocal is only a time field and shouldn't be used to search over datetimes. Instead use InsightsCases.actualStartTime if needing to search over datetimes.\n"
        "Do not append a time-dimension granularity to the end of a dimension name. It's currently broken\n"
        "Respond in markdown; show at most 50 rows if returning a table.\n"
    ),
    model="o3-mini",
    model_settings=ModelSettings(
        # temperature=0.0,
        parallel_tool_calls=True,
        reasoning=Reasoning(effort='low', summary='detailed'),
    ),
    tools=[cube_query, timezone_query, schema_search,
    #        FileSearchTool(
    #     vector_store_ids=["vs_6826450ca1b08191b711bc6257f5104f"],
    #     max_num_results=10
    # )
           ],
)


# ---------------------------------------------------------------------------
# 7. Convenience helper
# ---------------------------------------------------------------------------

async def ask_llm(question: str) -> str:
    """Blocking helper for REPL / testing."""
    result = Runner.run_streamed(agent, question)
    print("=== Run starting ===")

    async for event in result.stream_events():
        # We'll ignore the raw responses event deltas
        if event.type == "raw_response_event":
            continue
        # When the agent updates, print that
        elif event.type == "agent_updated_stream_event":
            print(f"Agent updated: {event.new_agent.name}")
            continue
        # When items are generated, print them
        elif event.type == "run_item_stream_event":
            if event.item.type == "tool_call_item":
                print("-- Tool was called")
            elif event.item.type == "tool_call_output_item":
                print(f"-- Tool output: {event.item.output}")
            elif event.item.type == "message_output_item":
                print(f"-- Message output:\n {ItemHelpers.text_message_output(event.item)}")
            else:
                pass  # Ignore other event types


# ---------------------------------------------------------------------------
# 8. CLI entrypoint
# ---------------------------------------------------------------------------

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="CubeJS assistant CLI")
    parser.add_argument("query", nargs="+", help="Your question")
    args = parser.parse_args()

    asyncio.run(ask_llm(args.query[0]))
    # client = OpenAI()
    # vector_store_id="vs_6826450ca1b08191b711bc6257f5104f"
    # results = client.vector_stores.search(
    #     vector_store_id=vector_store_id,
    #     query="what is a patient classification?",
    # )
    # print(results)
